import traceback
from abc import ABC, abstractmethod
from typing import List

from graphframes import <PERSON>raphFrame
from pyspark.sql import DataFrame
from yfflow import <PERSON><PERSON><PERSON><PERSON>ger

from backend.models import ClipParams, ClipResult, EngineEnum
from backend.utils import Spark

from .algorithms import GraphFrameKCoreAlgorithm, Neo4jBFSAlgorithm, Neo4jShortestPathAlgorithm, NodeTypeFilterAlgorithm
from .graph_loaders import GraphLoaderFactory

logger = YfLogger(__name__)


class BaseClipper(ABC):

    @abstractmethod
    def clip(self, params: ClipParams) -> ClipResult:     # type: ignore
        """裁剪图谱"""
        raise NotImplementedError


class SparkClipper:

    def clip(self, params: ClipParams) -> ClipResult:     # type: ignore
        """裁剪图谱"""
        with Spark() as spark:
            try:
                if not params.nodes:
                    return ClipResult(id=params.id)
                all_nodes, all_edges = GraphLoaderFactory.create(params.db).load(spark, params.space, params.version)
                logger.info(f'all: {all_nodes.count()=}, {all_edges.count()=}')

                start_nodes = all_nodes.filter(all_nodes.eid.isin(params.nodes))
                logger.info(f'start nodes: {start_nodes.collect()}')
                if start_nodes.count() == 0:
                    logger.info(f'传入节点不在图谱{params.space}中: {params.nodes}')
                    return ClipResult(id=params.id)

                # 扩展子图
                node_ids = self.extend_nodes(start_nodes, params)
                if len(node_ids) == 0:
                    return ClipResult(id=params.id)
                nodes = all_nodes.filter(all_nodes.id.isin(node_ids))
                edges = all_edges.filter(all_edges.src.isin(node_ids) & all_edges.dst.isin(node_ids))
                logger.info(f'extend: {nodes.count()=}, {edges.count()=}')

                # 过滤子图
                node_ids = self.filter_nodes(params, nodes, edges)
                if len(node_ids) == 0:
                    return ClipResult(id=params.id)
                nodes = nodes.filter(nodes.id.isin(node_ids))
                edges = edges.filter(edges.src.isin(node_ids) & edges.dst.isin(node_ids))
                logger.info(f'filter: {nodes.count()=}, {edges.count()=}')
                return ClipResult(
                    id=params.id,
                    nodes=[_.eid for _ in nodes.collect()],
                    edges=[_.id for _ in edges.collect()],
                    version=params.version
                )
            except Exception:
                traceback.print_exc()
                logger.error(f'图谱裁剪异常 => {traceback.format_exc()}')

    def extend_nodes(self, start_nodes: DataFrame, params: ClipParams) -> List[int]:
        """扩展节点"""
        node_ids = [_.id for _ in start_nodes.collect()]
        if params.sssp.enable:
            ids = Neo4jShortestPathAlgorithm.process(node_ids, params.space, params.version)
            node_ids = list(set(node_ids).union(ids))
        if params.bfs.enable:
            ids = Neo4jBFSAlgorithm.process(node_ids, params.space, params.bfs.depth, params.version)
            node_ids = list(set(node_ids).union(ids))
        return node_ids

    def filter_nodes(self, params: ClipParams, nodes: DataFrame, edges: DataFrame) -> List[int]:
        """过滤"""
        # TODO: 交集使用spark实现
        node_ids = [_.id for _ in nodes.collect()]
        if params.node_type.enable:
            filter_nodes = NodeTypeFilterAlgorithm.process(nodes, params.node_type.types)
            filter_ids = [_.id for _ in filter_nodes.collect()]
            node_ids = list(set(node_ids).intersection(filter_ids))
        if params.k_core.enable:
            gf = GraphFrame(nodes, edges)
            filter_nodes = GraphFrameKCoreAlgorithm.process(gf, (params.k_core.k, None))
            filter_ids = [_.id for _ in filter_nodes.collect()]
            node_ids = list(set(node_ids).intersection(filter_ids))
        return node_ids


class ClipperFactory:

    @staticmethod
    def create(engine: EngineEnum) -> SparkClipper:
        if engine == EngineEnum.SPARK:
            return SparkClipper()
        raise ValueError(f'不支持的计算引擎: {engine}')
