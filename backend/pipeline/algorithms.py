from itertools import combinations
from typing import List, Optional, Tuple

from graphframes import Graph<PERSON><PERSON><PERSON>
from pyspark.sql import <PERSON>Frame

from backend.utils import Neo4jClient

# TODO: 算法类抽象，结合配置


class GraphFrameShortestPathAlgorithm:

    @staticmethod
    def process(gf: GraphFrame, node_ids: List[int]) -> List[int]:
        """最短路径"""
        ids = []
        for node_pair in combinations(node_ids, 2):
            bfs_result = gf.bfs(f'id = {node_pair[0]}', f'id = {node_pair[1]}', maxPathLength=10)
            ids.extend([_.id for _ in bfs_result.select('id').collect()])
        return ids


class Neo4jShortestPathAlgorithm:
    neo4j_client = Neo4jClient()

    @classmethod
    def process(cls, node_ids: List[int], space: str, version: str) -> List[int]:
        """最短路径"""
        ids = []
        for node_pair in combinations(node_ids, 2):
            query = f"""
                MATCH path=shortestPath((m:{space}:{version})-[r*]-(n:{space}:{version}))
                WHERE ID(m) = {node_pair[0]} AND ID(n) = {node_pair[1]}
                AND ALL (x IN NODES(path)[1..-1] WHERE x._type <> '事物'  AND x <> m AND x <> n)
                WITH path, [rel in r WHERE rel.{version} = true] as rels
                WHERE size(rels) = size(r)
                RETURN nodes(path)
            """
            result = cls.neo4j_client.run(query)
            if not result:
                continue
            for item in result:
                ids.extend([_.id for _ in item['nodes(path)']])
        return ids


class GraphFrameBFSAlgorithm:

    @staticmethod
    def process(gf: GraphFrame, node_ids: List[int], depth: int) -> DataFrame:  # type: ignore
        """广度优先遍历"""
        result = gf.shortestPaths(landmarks=node_ids)
        df: DataFrame = result.select('id').where(f'min(map_values(distances)) <= {depth}')
        return df


class Neo4jBFSAlgorithm:
    neo4j_client = Neo4jClient()

    @classmethod
    def process(cls, node_ids: List[int], space: str, depth: int, version: str) -> List[int]:
        """广度优先遍历"""
        ids = []
        for node_id in node_ids:
            query = f"""
                MATCH path=(n:{space}:{version})-[r*1..{depth}]-(m:{space}:{version})
                WHERE id(n) = {node_id}
                AND n._type <> '事物' AND m._type <> '事物'
                UNWIND r as rel
                WITH rel,m
                WHERE rel.{version} = true
                RETURN m
            """
            result = cls.neo4j_client.run(query)
            if not result:
                continue
            ids.extend([_['m'].id for _ in result])
        return ids


class GraphFrameKCoreAlgorithm:

    @staticmethod
    def process(gf: GraphFrame, k_range: Tuple[int, Optional[int]]) -> DataFrame:
        """K-Core"""
        if k_range[1] is None:
            condition = f'degree >= {k_range[0]}'
        else:
            condition = f'degree >= {k_range[0]} and degree <= {k_range[1]}'
        return gf.degrees.filter(condition)    # type: ignore


class NodeTypeFilterAlgorithm:

    @staticmethod
    def process(nodes: DataFrame, node_types: List[str]) -> DataFrame:
        """节点类型过滤"""
        if len(node_types) == 0:
            return nodes
        return nodes.filter(nodes.type.isin(node_types))
