from neo4j import GraphDatabase
from omegaconf import OmegaConf
from pyspark.sql import SparkSession

conf = OmegaConf.load('conf/config.yaml')


class Spark:

    def __enter__(self):
        self.spark = SparkSession.builder \
            .master(conf.spark.master) \
            .appName(conf.spark.app_name) \
            .config('spark.jars', conf.spark.config.jars) \
            .config('spark.sql.debug.maxToStringFields', conf.spark.config.max_to_string_fields) \
            .getOrCreate()
        return self.spark

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.spark.stop()


class Neo4jClient:

    def __init__(self):
        self.graph = GraphDatabase.driver(conf.neo4j.url, auth=(conf.neo4j.username, conf.neo4j.password))

    def run(self, query: str) -> list:
        with self.graph.session() as session:
            result = session.run(query)
            return list(result)
