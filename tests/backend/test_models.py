from backend.models import ClipParams, ClipResult


class TestModels:

    def test_clip_params(self):
        params = ClipParams(id=1, space='test', nodes=['1', '2', '3'])
        assert params.space == 'test'
        assert params.nodes == ['1', '2', '3']

    def test_clip_result(self):
        result = ClipResult(id=1, nodes=['1', '2', '3'], edges=[1, 2, 3])
        assert result.id == 1
        assert result.nodes == ['1', '2', '3']
        assert result.edges == [1, 2, 3]
        assert result.dict() == {'id': 1, 'nodes': ['1', '2', '3'], 'edges': [1, 2, 3], 'version': 'c'}
