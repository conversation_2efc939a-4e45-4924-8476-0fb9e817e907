from abc import ABC, abstractmethod

from pyspark.sql import <PERSON>Frame, SparkSession
from yfflow import <PERSON><PERSON><PERSON><PERSON><PERSON>

from backend.models import DbEnum
from backend.utils import conf

logger = YfLogger(__name__)


class BaseGraphLoader(ABC):

    @classmethod
    def load(cls, spark_session: SparkSession, space: str, version: str) -> tuple:
        """加载图谱"""
        nodes = cls.load_nodes(spark_session, space, version)
        edges = cls.load_edges(spark_session, space, version)
        logger.info(f'{nodes.count()=}, {edges.count()=}')
        return nodes, edges

    @staticmethod
    @abstractmethod
    def load_nodes(spark_session: SparkSession, space: str, version: str) -> DataFrame:
        """读取所有节点"""
        raise NotImplementedError

    @staticmethod
    @abstractmethod
    def load_edges(spark_session: SparkSession, space: str, version: str) -> DataFrame:
        """读取所有边"""
        raise NotImplementedError


class Neo4jGraphLoader(BaseGraphLoader):

    @staticmethod
    def load_nodes(spark_session: SparkSession, space: str, version: str) -> DataFrame:
        """读取所有节点"""
        query = f"""
            MATCH (n:{space}:{version}) WHERE n.name<>'事物' or n._type is null and n._type <> '事物'
            RETURN id(n) as id, n._eid AS eid, n._type AS type
        """
        nodes = spark_session.read.format(conf.neo4j.format) \
            .option('url', conf.neo4j.url) \
            .option('authentication.basic.username', conf.neo4j.username) \
            .option('authentication.basic.password', conf.neo4j.password) \
            .option('query', query) \
            .load()
        nodes = nodes.withColumn('id', nodes['id'].cast('int'))
        return nodes

    @staticmethod
    def load_edges(spark_session: SparkSession, space: str, version: str) -> DataFrame:
        """读取所有边"""
        query = f"""
            MATCH (n:{space}:{version})-[r:关联]->(m:{space}:{version})
            WHERE r.{version}=true
            RETURN id(r) as id, id(n) as src, id(m) as dst, r.name as relation
        """
        edges = spark_session.read.format(conf.neo4j.format) \
            .option('url', conf.neo4j.url) \
            .option('authentication.basic.username', conf.neo4j.username) \
            .option('authentication.basic.password', conf.neo4j.password) \
            .option('query', query) \
            .load()
        edges = edges.withColumn('id', edges['id'].cast('int')) \
            .withColumn('src', edges['src'].cast('int')) \
            .withColumn('dst', edges['dst'].cast('int'))
        return edges


class GraphLoaderFactory:

    @staticmethod
    def create(db: DbEnum) -> BaseGraphLoader:
        if db == DbEnum.NEO4j:
            return Neo4jGraphLoader     # type: ignore
        raise ValueError(f'不支持的图数据库: {db}')
