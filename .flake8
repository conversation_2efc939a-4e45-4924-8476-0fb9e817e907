[flake8]
select = B,C,E,F,P,T4,W,B9,N,C4,T0,A0,N4,CF,R,SIM,H,VNE,ECE,CCR,A5,AAA,ANN
ignore =
    W503,W504,H101,P101,<PERSON>R001,E800,B006,<PERSON>416,B007,<PERSON>E001,<PERSON>414,B902,P103,B902,ANN101
    W503,W504,C416,E800,CCR001,P101,ANN102,ANN001,ANN206,ANN205,VNE003,VNE002,A003,ANN202,R504,SIM113,R503,AAA05,R501,SIM117,SIM106,SIM115,C901,N806
    AAA02,AAA04,AAA03,ANN401,SIM300,SIM120,VNE001,A002,A001,ANN204,SIM102,N803,SIM401,<PERSON>IM210,<PERSON>IM118,<PERSON>IM105,T001,N801,PT009,ANN203,B006,<PERSON>400,<PERSON><PERSON>118,<PERSON><PERSON>201,P103,<PERSON>802,<PERSON>904,<PERSON>805,B902,<PERSON>011,<PERSON>IM110,<PERSON>IM904,N818,<PERSON>502,H238,H301,H304,H306,H401,H404,H405,Q000,AAA01,ANN002,ANN003,ANN101,ANN201,ECE001,E122,B907
exclude = env/*,scripts/*,tests/*
max-line-length = 120
max-complexity = 12
max-expression-complexity = 6
max-cognitive-complexity = 30
