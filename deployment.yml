apiVersion: apps/v1beta1
kind: StatefulSet
metadata:
  name: &PROJECT_NAME
  namespace: default
spec:
  replicas: 1
  serviceName: &PROJECT_NAME
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        author: yunfu
        name: &PROJECT_NAME
      annotations:
        sha: '&CI_COMMIT_SHORT_SHA'
    spec:
      containers:
        - image: registry.cn-beijing.aliyuncs.com/yunfutech/yfflow:centos-1.0.2
          imagePullPolicy: IfNotPresent
          workingDir: /app
          command:
            - /env/bin/python
            - -m
            - scripts.flow
          securityContext:
            privileged: true
          name: &PROJECT_NAME
          volumeMounts:
            - mountPath: &MOUNT_PATH
              name: yunfu
            - mountPath: /env
              name: env
            - mountPath: /app
              name: app
            - mountPath: /app/logs
              name: logs
          env:
            - name: YUNFU_LOG_CONFIG
              value: conf/yfflow_logging.yaml
          resources:
            limits:
              cpu: 5000m
              memory: 8000Mi
            requests:
              cpu: 100m
              memory: 100Mi
          readinessProbe:
            tcpSocket:
              port: 8000
            initialDelaySeconds: 30
            periodSeconds: 15
            timeoutSeconds: 1
          livenessProbe:
            tcpSocket:
              port: 8000
            initialDelaySeconds: 600
            periodSeconds: 15
            timeoutSeconds: 1
      volumes:
        - name: yunfu
          hostPath:
            path: &MOUNT_PATH
        - name: env
          hostPath:
            path: &MOUNT_PATH/env
        - name: app
          hostPath:
            path: &MOUNT_PATH/app
        - name: logs
          hostPath:
            path: &MOUNT_PATH/logs

---
apiVersion: v1
kind: Service
metadata:
  name: &PROJECT_NAME
  namespace: default
spec:
  type: ClusterIP
  clusterIP: None
  selector:
    name: &PROJECT_NAME
  ports:
    - name: &PROJECT_NAME
      port: 8000
      targetPort: 8000
    - name: &PROJECT_NAME-metrics
      port: 8080
      targetPort: 8080

---
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  labels:
    k8s-app: &PROJECT_NAME
    sha: "&CI_COMMIT_SHORT_SHA"
  name: &PROJECT_NAME
spec:
  endpoints:
  - interval: 30s
    port: &PROJECT_NAME-metrics
    path: /metrics
  selector:
    name: &PROJECT_NAME
