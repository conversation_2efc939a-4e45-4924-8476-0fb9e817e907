from typing import Dict

import httpx
from docarray import DocumentArray
from yfflow import <PERSON><PERSON><PERSON>xecutor, requests

from backend.models import ClipParams
from backend.pipeline import ClipperFactory
from backend.utils import conf


class SaveExecutor(YfExecutor):

    @requests()
    async def execute(self, docs: DocumentArray, **kwargs: Dict[str, dict]) -> DocumentArray:
        for doc in docs:
            await self._clip_graph(doc.tags)

    async def _clip_graph(self, tags):
        try:
            params = ClipParams(**tags)
            self.logger.info(f'clip params: {params}')
            result = ClipperFactory.create(params.engine).clip(params)
            self.logger.info(f'clip result: {result}')
            async with httpx.AsyncClient() as client:
                response = await client.post(conf.notice.url, json=result.dict(), timeout=conf.notice.timeout)
                self.logger.info(f'notice response: {response}')
                if response.status_code != 200:
                    raise Exception(f'notice failed: {response.text}')
        except Exception as e:
            self.logger.error(f"save failed => {e}")
