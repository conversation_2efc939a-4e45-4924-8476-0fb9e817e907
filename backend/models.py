from enum import Enum
from typing import List

from pydantic import BaseModel, validator


class DbEnum(Enum):
    NEO4j = 'neo4j'
    NEBULA = 'nebula'


class EngineEnum(Enum):
    SPARK = 'spark'


class KCoreConfig(BaseModel):
    enable: bool = False
    k: int = 1


class BFSConfig(BaseModel):
    enable: bool = False
    depth: int = 1


class SSSPConfig(BaseModel):
    enable: bool = False


class LouvainConfig(BaseModel):
    enable: bool = False


class NodeTypesConfig(BaseModel):
    enable: bool = False
    types: List[str] = []


class ClipParams(BaseModel):
    id: int                                 # 子图ID
    space: str                              # 原图谱空间
    version: str = 'c'                      # 版本
    db: DbEnum = DbEnum.NEO4j               # 数据库类型
    engine: EngineEnum = EngineEnum.SPARK   # 计算引擎
    nodes: List[str] = []                   # 起始点
    k_core: KCoreConfig = KCoreConfig()     # k_core配置
    bfs: BFSConfig = BFSConfig()            # bfs配置
    sssp: SSSPConfig = SSSPConfig()         # sssp配置
    louvaiin: LouvainConfig = LouvainConfig()       # louvaiin配置
    node_type: NodeTypesConfig = NodeTypesConfig()  # 实体类型配置

    class Config:
        use_enum_values = True


class ClipResult(BaseModel):
    id: int                 # 子图ID
    nodes: List[str] = []   # 节点
    edges: List[int] = []   # 边
    version: str = 'c'      # 版本

    def __str__(self) -> str:
        return f'ClipResult<id={self.id}, nodes={self.nodes[:10]}..., edges={self.edges[:10]}...>'

    @validator('edges')
    def convert_to_int(cls, v):
        return [int(_) for _ in v]


class CoordinatesParams(BaseModel):
    id: int                                 # 子图ID
    space: str                              # 原图谱空间
    version: str = 'c'                      # 版本
    coordinates: List[str] = []                   # 起始点

    class Config:
        use_enum_values = True
